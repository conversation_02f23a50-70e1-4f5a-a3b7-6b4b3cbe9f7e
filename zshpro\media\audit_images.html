<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastGPT更新错误图片(审核)</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background-color: #f9f9f9;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
        }

        th, td {
            border: none;
            padding: 12px 15px;
            text-align: left;
        }

        th {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
        }

        tr {
            background-color: #fff;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        tr:nth-child(odd) {
            background-color: #f2f2f2; /* 浅灰色背景 */
        }

        tr:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination button {
            margin: 0 5px;
            padding: 8px 15px;
            cursor: pointer;
            border: none;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .pagination button:hover {
            background-color: #45a049;
        }

        .pagination button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        textarea {
            width: 100%;
            padding: 10px;
            margin-top: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            resize: vertical;
        }

        button#saveFix {
            margin-top: 10px;
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button#saveFix:hover {
            background-color: #45a049;
        }

        /* 骨架屏样式 */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        /* 新增样式 */
        .answer-content {
            max-height: 100px; /* 设置最大高度 */
            overflow: hidden;
            position: relative;
        }

        .answer-content.expanded {
            max-height: none;
        }

        .show-more-btn {
            cursor: pointer;
            color: #4CAF50;
            text-decoration: underline;
        }

        /* 日期过滤控件样式 */
        .date-filter {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
        }

        .date-filter label {
            margin-right: 10px;
            font-weight: bold;
            color: #333;
        }

        .date-filter input[type="date"] {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 14px;
        }

        .date-filter button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .date-filter button:hover {
            background-color: #45a049;
        }

        /* 加载中模态框样式 */
        #loadingModal {
            display: none;
            position: fixed;
            z-index: 2;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        #loadingModal .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 10px;
            border: 1px solid #888;
            width: 20%;
            max-width: 200px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        #loadingModal h2 {
            color: #aaa;
            font-size: 18px;
            margin: 0;
        }

        /* 上传图片模态框样式 */
        #uploadModal .modal-content {
            width: 80%;
            max-width: 800px;
        }

        #uploadTable {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        #uploadTable th, #uploadTable td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        #uploadTable img {
            max-width: 100px;
            height: auto;
        }

        #uploadImageButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 15px;
            cursor: pointer;
            border-radius: 4px;
        }

        #uploadImageButton:hover {
            background-color: #45a049;
        }
        .answer-content img {
            max-width: 100%;
            height: auto;
        }
        
        .delete-button {
            background-color: #ff4d4d;
            color: white;
            border: none;
            padding: 3px 8px;
            cursor: pointer;
            border-radius: 4px;
            margin-left: 5px;
        }

        .delete-button:hover {
            background-color: #cc0000;
        }
        .approveButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 3px 8px;
            cursor: pointer;
            border-radius: 4px;
            margin-left: 5px;
        }

        .approveButton:hover {
            background-color: #45a049;
        }

        #bulkApproveButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 15px;
            cursor: pointer;
            border-radius: 4px;
            margin-left: 5px;
        }

        #bulkApproveButton:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>FastGPT更新错误图片(审核)</h1>
    <div class="date-filter">
        <label for="appIdSelect" style="display: none;">选择知识库:</label>
        <select id="appIdSelect" style="display: none;">
        </select>
        <label for="fileSelect" style="display: none;">集合名称:</label>
        <select id="fileSelect" style="display: none;">
        </select>
        <label for="startDate" style="display: none;">开始时间:</label>
        <input type="date" id="startDate" style="display: none;">
        <label for="endDate" style="display: none;">结束时间:</label>
        <input type="date" id="endDate" style="display: none;">
        <button id="filterButton" style="display: none;">查询</button>
        <button id="bulkApproveButton">一键通过</button>
        <div>&nbsp;&nbsp;</div>
        <!-- <button id="exportButton">导出Excel</button> -->
    </div>
    <div class="table-container">
        <table id="dataTable">
            <thead>
                <tr>
                    <th>编号</th>
                    <th>时间</th>
                    <th>知识库</th>
                    <th>集合</th>
                    <th>片段</th>
                    <th>待更新图片</th>
                    <th>审核状态</th>
                    <th>上传</th>
                </tr>
            </thead>
            <tbody>
                <!-- 表格数据将通过JavaScript动态生成 -->
            </tbody>
        </table>
    </div>
    <div class="pagination">
        <button id="prevPage">上一页</button>
        <span id="pageInfo"></span>
        <button id="nextPage">下一页</button>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>输入修复内容</h2>
            <textarea id="fixContent" rows="4" cols="50"></textarea>
            <br>
            <button id="saveFix">保存</button>
        </div>
    </div>

    <!-- 上传图片模态框 -->
    <div id="uploadModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeUploadModal()">&times;</span>
            <h2>上传图片</h2>
            <input type="file" id="imageUpload" accept="image/jpeg, image/png" style="display: none;">
            <table id="uploadTable">
                <thead>
                    <tr>
                        <th>原始图片</th>
                        <th>更新标记</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 上传图片的内容将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div id="loadingModal" class="modal">
        <div class="modal-content">
            <h2>加载中...</h2>
        </div>
    </div>
    <script src="/media/js/FileSaver.min.js"></script>
    <script src="/media/js/script_audit_images.js"></script>
    <script src="/media/js/xlsx.full.min.js"></script>
</body>
</html>