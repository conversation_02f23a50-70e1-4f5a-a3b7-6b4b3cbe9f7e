const itemsPerPage = 5; // 每页显示的行数
let currentPage = 1;
let currentRowIndex = -1; // 当前操作的行索引
let data = []; // 存储从接口获取的数据
let total = 0; // 获取到的总数据

// 获取数据的接口地址
const appListApiUrl = 'http://192.168.10.51:8009/getDatasList';
const fileListApiUrl = 'http://192.168.10.51:8009/getcollectionsList';
const apiUrl1 = 'http://192.168.10.51:8009/getDatasetsDatas_images/';
const apiUrl3 = 'http://192.168.10.51:8009/uploadimage/';
const auditApiUrl = 'http://192.168.10.51:8009/whauditimage/';

// const appListApiUrl = 'http://106.63.8.99:8005/getDatasList';
// const fileListApiUrl = 'http://106.63.8.99:8005/getcollectionsList';
// const apiUrl1 = 'http://106.63.8.99:8005/getDatasetsDatas_images/';
// const apiUrl3 = 'http://106.63.8.99:8005/uploadimage/';
// const auditApiUrl = 'http://106.63.8.99:8005/whauditimage/';

const headers = { 'Content-Type': 'application/json' };

// 获取应用列表并填充到下拉选择框
async function fetchAppList() {
    try {
        const response = await fetch(appListApiUrl, {
            method: 'GET',
            headers: headers
        });
        const res = await response.json();
        if (res.code !== 200) {
            throw new Error('获取知识库列表失败');
        } else {
            const appList = res.data;
            const appIdSelect = document.getElementById("appIdSelect");
            appList.forEach(app => {
                const option = document.createElement("option");
                option.value = app.id;
                option.textContent = app.name;
                appIdSelect.appendChild(option);
            });
            // 默认选择第一个应用
            if (appList.length > 0) {
                appIdSelect.value = appList[0].id;
                await fetchFileList(appList[0].id); // 获取集合列表
                fetchDataAndDisplayTable(); // 重新获取数据并更新表格
            }
        }
    } catch (error) {
        console.error('获取知识库列表失败:', error);
    }
}

// 查询集合名称并填充到 fileSelect 下拉框
async function fetchFileList(appId) {
    try {
        const response = await fetch(fileListApiUrl + "?datasetId=" + appId, {
            method: 'GET',
            headers: headers
        });
        const res = await response.json();
        if (res.code !== 200) {
            throw new Error('获取集合列表失败');
        } else {
            const fileList = res.data;
            const fileSelect = document.getElementById("fileSelect");
            fileSelect.innerHTML = ""; // 清空现有的选项
            fileList.forEach(file => {
                const option = document.createElement("option");
                option.value = file.id;
                option.textContent = file.name;
                fileSelect.appendChild(option);
            });
            fetchDataAndDisplayTable(); // 重新获取数据并更新表格
        }
    } catch (error) {
        console.error('获取集合列表失败:', error);
    }
}

// 当知识库选择发生变化时，触发查询集合名称的接口
document.getElementById("appIdSelect").addEventListener("change", async function () {
    const selectedAppId = this.value;
    if (selectedAppId) {
        await fetchFileList(selectedAppId);
    }
});

// 根据软件请求地址获取数据
async function fetchDataAndDisplayTable() {
    try {
        showLoadingModal();
        const startDate = new Date(document.getElementById("startDate").value);
        const endDate = new Date(document.getElementById("endDate").value);
        if (startDate > endDate) {
            alert("开始时间不能大于结束时间");
            return;
        }
        var selectedCollectionId = document.getElementById("fileSelect").value;
        if (!selectedCollectionId) {
            const selectedAppId = document.getElementById("appIdSelect").value;
            await fetchFileList(selectedAppId);
            selectedCollectionId = document.getElementById("fileSelect").value;
        }

        // 计算 offset 和 limit
        const offset = (currentPage - 1) * itemsPerPage;
        const limit = itemsPerPage;
        const isaudit = 1

        let apiUrl11 = apiUrl1 + "?dateStart=" + startDate.toISOString() + "&dateEnd=" + endDate.toISOString() + "&collectionId=" + selectedCollectionId + "&offset=" + offset + "&limit=" + limit+"&isaudit="+isaudit;
        const response = await fetch(apiUrl11, { method: 'GET' });
        const res = await response.json();
        if (res.code !== 200) {
            console.log('res-----error-------11=', res);
            throw new Error('获取数据失败');
        } else {
            data = res.data.data; // 直接使用返回的数据
            total = res.data.total; // 获取总数据量
            displayTable(currentPage);
        }
    } catch (error) {
        console.error('获取数据失败:', error);
    } finally {
        hideLoadingModal();
    }
}

function displayTable(page) {
    const start = (page - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const paginatedData = data //.slice(start, end);
    const tableBody = document.querySelector("#dataTable tbody");
    tableBody.innerHTML = "";

    paginatedData.forEach((item, index) => {
        const row = document.createElement("tr");
        row.innerHTML = `
            <td style="width: 5%">${item.id}</td>
            <td style="width: 5%">${item.updateTime}</td>
            <td style="width: 5%">${item.datasetName}</td>
            <td style="width: 5%">${item.collectionName}</td>
            <td style="width: 25%;">
                <div class="answer-content">${item.q}</div>
                <div class="show-more-btn" data-index="${index}">显示更多</div>
            </td>
            <td>${item.uploadimages_str}</td>
            <td style="width: 5%">${item.audit==0?"未审核":"已审核"}</td>
            <td style="width: 10%">
                <button class="editButton" data-index="${index}">操作</button>
                <button class="approveButton" data-index="${index}">通过</button>
                <button class="editButton_qc" data-index="${index}">清除</button>
            </td>
        `;
        tableBody.appendChild(row);
    });

    document.getElementById("pageInfo").textContent = `第 ${page} 页，共 ${Math.ceil(total / itemsPerPage)} 页`;

    // 控制“上一页”和“下一页”按钮的禁用状态
    const prevPageButton = document.getElementById("prevPage");
    const nextPageButton = document.getElementById("nextPage");

    prevPageButton.disabled = page === 1;
    nextPageButton.disabled = page >= Math.ceil(total / itemsPerPage);

    // 添加操作按钮的事件监听器
    const editButtons = document.querySelectorAll(".editButton");
    editButtons.forEach(button => {
        button.addEventListener("click", () => {
            currentRowIndex = parseInt(button.getAttribute("data-index"));
            openUploadModal();
        });
    });

    // 为“通过”按钮添加事件监听器
    const approveButtons = document.querySelectorAll(".approveButton");
    approveButtons.forEach(button => {
        button.addEventListener("click", () => {
            console.log("Approving item...");
            const index = parseInt(button.getAttribute("data-index"));
            const id = data[index].id; // 获取当前行的编号
            approveItem(id,0);
        });
    });

    // 添加清除按钮的事件监听器
    const editButtons_qc = document.querySelectorAll(".editButton_qc");
    editButtons_qc.forEach(button => {
        button.addEventListener("click", () => {
            currentRowIndex = parseInt(button.getAttribute("data-index"));
            const id = data[currentRowIndex].id; // 获取当前行的编号
            // 确认删除
            if (confirm("确定要还原这个片段内所有对图片未审核的操作吗？")) {
                // 发送删除请求到后台接口
                const picid_list = [];
                for (let kke in data[currentRowIndex].images_dict) {
                    picid_list.push(data[currentRowIndex].images_dict[kke].picid);
                }
                let requestBody = { "picid": picid_list, "datasets_dataid": id, "type":"3" };
                fetch(apiUrl3, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(requestBody) })
                    .then(response => response.json())
                    .then(dataes => {
                        if (dataes.code == 200) {
                            alert('保存成功');
                            fetchDataAndDisplayTable(); // 重新获取数据并更新表格
                        } else if (dataes.code == 520) {
                            alert('重复提交');
                        } else {
                            alert('保存失败');
                        }
                    });
            }
        });
    });

    // 添加显示更多按钮的事件监听器
    const showMoreButtons = document.querySelectorAll(".show-more-btn");
    showMoreButtons.forEach(button => {
        button.addEventListener("click", () => {
            const index = parseInt(button.getAttribute("data-index"));
            const answerContent = button.previousElementSibling;
            if (answerContent.classList.contains("expanded")) {
                answerContent.classList.remove("expanded");
                button.textContent = "显示更多";
            } else {
                answerContent.classList.add("expanded");
                button.textContent = "收起";
            }
        });
    });
}

document.getElementById("prevPage").addEventListener("click", () => {
    if (currentPage > 1) {
        currentPage--;
        fetchDataAndDisplayTable();
    }
});

document.getElementById("nextPage").addEventListener("click", () => {
    if (currentPage < Math.ceil(total / itemsPerPage)) {
        currentPage++;
        fetchDataAndDisplayTable();
    }
});

// 日期过滤功能
document.getElementById("filterButton").addEventListener("click", () => {
    currentPage = 1; // 重置为第一页
    fetchDataAndDisplayTable();
});

// 显示加载中模态框
function showLoadingModal() {
    document.getElementById("loadingModal").style.display = "block";
}

// 隐藏加载中模态框
function hideLoadingModal() {
    document.getElementById("loadingModal").style.display = "none";
}

// 打开上传图片的模态框
function openUploadModal() {
    if (currentRowIndex !== -1) {
        const images_dict = data[currentRowIndex].images_dict;
        const uploadTableBody = document.querySelector("#uploadTable tbody");
        uploadTableBody.innerHTML = ""; // 清空现有的内容

        // 遍历 images_dict 并显示每一张图片
        for (const key in images_dict) {
            if (images_dict.hasOwnProperty(key)) {
                const imageData = images_dict[key];
                const row = document.createElement("tr");
                row.innerHTML = `
                    <td class="picid" style="display:none">${imageData.picid}</td>
                    <td>${imageData.src}</td>
                    <td>${imageData.updateimage}</td>
                    <td>
                        <button class="quchu-button" onclick="quchuImage(this)">清除</button>
                        <button onclick="uploadImage(this)">上传</button>
                        <button class="delete-button" onclick="deleteImage(this)">删除</button>
                    </td>
                `;
                uploadTableBody.appendChild(row);
            }
        }
        // 重置文件输入框的值
        document.getElementById("imageUpload").value = "";
        // 显示模态框
        document.getElementById("uploadModal").style.display = "block";
    }
}

// 关闭上传图片的模态框
function closeUploadModal() {
    document.getElementById("uploadModal").style.display = "none";
    document.getElementById("imageUpload").value = ""; // 清除文件输入框的值
}

// 上传图片到服务器
function uploadImage(button) {
    const fileInput = document.getElementById("imageUpload");
    const ddid = data[currentRowIndex].id;

    // 移除之前的事件监听器（如果存在）
    fileInput.removeEventListener('change', handleFileUpload);

    // 定义文件上传处理函数
    function handleFileUpload() {
        const file = fileInput.files[0];
        if (file) {
            if (file.type === "image/jpeg" || file.type === "image/png") {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const imageUrl = e.target.result;
                    const row = button.parentElement.parentElement;
                    const picid = row.querySelector(".picid").textContent;

                    // 更新图片预览
                    const uploadedImage = row.querySelector(".uploadedImage");
                    if (uploadedImage) {
                        uploadedImage.src = imageUrl;
                    }

                    // 上传图片到服务器
                    let requestBody = { "picid": picid, "datasets_dataid": ddid, "fix": imageUrl, "type": "0" };
                    fetch(apiUrl3, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(requestBody) })
                        .then(response => response.json())
                        .then(dataes => {
                            if (dataes.code == 200) {
                                alert('保存成功');
                                fetchDataAndDisplayTable(); // 重新获取数据并更新表格
                            } else if (dataes.code == 520) {
                                alert('重复提交');
                            } else {
                                alert('保存失败');
                            }
                        });
                };
                reader.readAsDataURL(file);
            } else {
                alert("仅支持上传 JPG 和 PNG 格式的图片");
            }
        } else {
            alert("请选择要上传的图片");
        }

        // 上传完成后移除事件监听器
        fileInput.removeEventListener('change', handleFileUpload);
    }

    // 绑定新的事件监听器
    fileInput.addEventListener('change', handleFileUpload);

    // 触发文件选择对话框
    fileInput.click();
}

// 删除图片
function deleteImage(button) {
    const row = button.parentElement.parentElement;
    const picid = row.querySelector(".picid").textContent;
    const id = data[currentRowIndex].id; // 获取当前行的编号

    // 确认删除
    if (confirm("确定要删除这张图片吗？")) {
        // 发送删除请求到后台接口
        let requestBody = { "picid": picid, "datasets_dataid": id, "type":"1" };
        fetch(apiUrl3, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(requestBody) })
            .then(response => response.json())
            .then(dataes => {
                if (dataes.code == 200) {
                    const imageElement = row.querySelector(".uploadedImage");
                    if (imageElement) {
                        imageElement.src = "";
                        imageElement.alt = "已标记删除";
                    } else {
                        console.error("未找到 .uploadedImage 元素");
                    }
                    alert('保存成功');
                    fetchDataAndDisplayTable(); // 重新获取数据并更新表格
                } else if (dataes.code == 520) {
                    alert('重复提交');
                } else {
                    alert('保存失败');
                }
            });
    }
}

async function approveItem(id,tag) {
    try {
        showLoadingModal();
        const response = await fetch(auditApiUrl, {method: 'POST',headers: headers,body: JSON.stringify({ id: id,tag:tag })});
        const res = await response.json();
        if (res.code !== 200) {
            throw new Error('通过失败');
        } else {
            alert(res.msg);
            fetchDataAndDisplayTable(); // 重新获取数据并更新表格
        }
    } catch (error) {
        console.error('通过失败:', error);
    } finally {
        hideLoadingModal();
    }
}

function quchuImage(button) {
    const row = button.parentElement.parentElement;
    const picid = row.querySelector(".picid").textContent;
    const id = data[currentRowIndex].id; // 获取当前行的编号
    // 发送删除请求到后台接口
    let requestBody = { "picid": picid, "datasets_dataid": id, "type":"2" };
    fetch(apiUrl3, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(requestBody) })
        .then(response => response.json())
        .then(dataes => {
            if (dataes.code == 200) {
                const imageElement = row.querySelector(".uploadedImage");
                if (imageElement) {
                    imageElement.src = "";
                    imageElement.alt = "";
                } else {
                    console.error("未找到 .uploadedImage 元素");
                }
                alert('保存成功');
                fetchDataAndDisplayTable(); // 重新获取数据并更新表格
            } else if (dataes.code == 520) {
                alert('重复提交');
            } else {
                alert('保存失败');
            }
        });
}


// 初始化页面
document.addEventListener("DOMContentLoaded", () => {
    // 获取当前日期
    const today = new Date();
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(today.getDate() - 3);

    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    // 设置日期控件的默认值
    document.getElementById("startDate").value = formatDate(threeDaysAgo);
    document.getElementById("endDate").value = formatDate(today);

    // 初始化表格数据
    fetchAppList().then(() => {
        fetchDataAndDisplayTable();
    });
        // 为“一键通过”按钮添加事件监听器
    document.getElementById("bulkApproveButton").addEventListener("click", () => {
        const selectedCollectionId = document.getElementById("fileSelect").value;
        if (selectedCollectionId) {
            approveItem(selectedCollectionId,1); // 传入集合ID
        } else {
            alert("请先选择一个集合");
        }
    });
});