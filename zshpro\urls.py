# urls.py
from fastapi import APIRouter
from zhishiku_h5 import *
from images_h5 import *
from log_h5 import *

router = APIRouter()
# 定义 URL 路径与方法的映射  zhishiku_h5.py
router.add_api_route("/", index, methods=["GET"])
router.add_api_route("/index/", index, methods=["GET"])
router.add_api_route("/login/", login, methods=["POST"])
router.add_api_route("/deleteQA/", deleteQA, methods=["POST"])
router.add_api_route("/getDatasList/", getDatasList, methods=["GET"])
router.add_api_route("/uploadfiles/", uploadfiles, methods=["POST"])
router.add_api_route("/getCollectionListInfo/", getCollectionListInfo, methods=["GET"])
router.add_api_route("/auditCollection/", auditCollection, methods=["POST"])
router.add_api_route("/getDatasetdatas/", getDatasetdatas, methods=["GET"])
router.add_api_route("/deleteCollection/", deleteCollection, methods=["GET"])
router.add_api_route("/getSameNameFiles/", getSameNameFiles, methods=["GET"])
router.add_api_route("/updateDatasetdatas/", updateDatasetdatas, methods=["POST"])
router.add_api_route("/getFileInfo/", getFileInfo, methods=["GET"])


# 定义 URL 路径与方法的映射  images_h5.py
router.add_api_route("/h5_images/", h5_images, methods=["GET"])
router.add_api_route("/h5_images_audit_wh/", h5_images_audit_wh, methods=["GET"])
router.add_api_route("/getcollectionsList/", getcollectionsList, methods=["GET"])
router.add_api_route("/getDatasetsDatas_images/", getDatasetsDatas_images, methods=["GET"])
router.add_api_route("/uploadimage/", uploadimage, methods=["POST"])
router.add_api_route("/whauditimage/", whauditimage, methods=["POST"])

# 定义 URL 路径与方法的映射  log_h5.py
router.add_api_route("/applist/", applist, methods=["GET"])
router.add_api_route("/h5_log/", h5_log, methods=["GET"])
router.add_api_route("/getChatLogs/", getChatLogs, methods=["GET"])
router.add_api_route("/saveFeedbacks/", saveFeedbacks, methods=["POST"])
router.add_api_route("/getPaginationRecords/", getPaginationRecords, methods=["POST"])

# 定义 标书解读 接口地址
router.add_api_route("/savebs/", savebs, methods=["POST"])